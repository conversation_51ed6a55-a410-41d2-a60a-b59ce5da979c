package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Web代理配置管理控制器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/proxy/config")
public class WebProxyConfigController {

    @Autowired
    private WebProxyConfigService configService;

    /**
     * 获取所有代理配置
     */
    @GetMapping("/list")
    public ResponseEntity<List<WebProxyConfig>> getAllConfigs() {
        List<WebProxyConfig> configs = configService.getAllActiveConfigs();
        return ResponseEntity.ok(configs);
    }

    /**
     * 根据ID获取代理配置
     */
    @GetMapping("/{id}")
    public ResponseEntity<WebProxyConfig> getConfigById(@PathVariable Long id) {
        // 这里需要添加根据ID查询的方法
        return ResponseEntity.notFound().build();
    }

    /**
     * 根据端口获取代理配置
     */
    @GetMapping("/port/{port}")
    public ResponseEntity<WebProxyConfig> getConfigByPort(@PathVariable Integer port) {
        WebProxyConfig config = configService.getConfigByPort(port);
        if (config != null) {
            log.info("查询端口{}的配置: 名称={}, 页面水印={}, API水印={}, 水印文本={}, API路径模式={}",
                    port, config.getProxyName(),
                    config.getEnablePageWatermark(),
                    config.getEnableApiWatermark(),
                    config.getWatermarkText(),
                    config.getApiPathPatterns());
            return ResponseEntity.ok(config);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 创建代理配置
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createConfig(@Valid @RequestBody WebProxyConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "代理配置创建成功");
            result.put("data", savedConfig);
            
            log.info("创建代理配置成功: {} -> {}:{}", 
                    savedConfig.getProxyPort(), 
                    savedConfig.getTargetHost(), 
                    savedConfig.getTargetPort());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建代理配置失败: " + e.getMessage());
            
            log.error("创建代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 更新代理配置
     */
    @PutMapping("/update")
    public ResponseEntity<Map<String, Object>> updateConfig(@Valid @RequestBody WebProxyConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig updatedConfig = configService.updateConfig(config);
            result.put("success", true);
            result.put("message", "代理配置更新成功");
            result.put("data", updatedConfig);
            
            log.info("更新代理配置成功: {}", updatedConfig.getProxyName());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新代理配置失败: " + e.getMessage());
            
            log.error("更新代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 删除代理配置
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.deleteConfig(id);
            result.put("success", true);
            result.put("message", "代理配置删除成功");
            
            log.info("删除代理配置成功: {}", id);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除代理配置失败: " + e.getMessage());
            
            log.error("删除代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 启用代理配置
     */
    @PostMapping("/enable/{id}")
    public ResponseEntity<Map<String, Object>> enableConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.enableConfig(id);
            result.put("success", true);
            result.put("message", "代理配置启用成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "启用代理配置失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 禁用代理配置
     */
    @PostMapping("/disable/{id}")
    public ResponseEntity<Map<String, Object>> disableConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.disableConfig(id);
            result.put("success", true);
            result.put("message", "代理配置禁用成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "禁用代理配置失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取配置统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getConfigStats() {
        Map<String, Object> stats = configService.getConfigStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.refreshCache();
            result.put("success", true);
            result.put("message", "配置缓存刷新成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "刷新配置缓存失败: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 快速创建百度代理配置
     */
    @PostMapping("/create-baidu")
    public ResponseEntity<Map<String, Object>> createBaiduProxy(@RequestParam(defaultValue = "8081") Integer port,
                                                               @RequestParam(defaultValue = "百度代理水印") String watermarkText) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig config = new WebProxyConfig();
            config.setProxyName("百度代理");
            config.setProxyPort(port);
            config.setTargetHost("www.baidu.com");
            config.setTargetPort(443);
            config.setTargetProtocol("https");
            config.setWatermarkText(watermarkText);
            config.setEnablePageWatermark(true);
            config.setEnableApiWatermark(true);
            config.setWatermarkOpacity(0.15);
            config.setWatermarkColor("#FF0000");
            config.setWatermarkAngle(-30.0);
            config.setRemark("百度网站代理，自动添加水印");
            config.setCreateUser("system");
            
            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "百度代理配置创建成功");
            result.put("data", savedConfig);
            result.put("accessUrl", "http://localhost:" + port);
            
            log.info("创建百度代理配置成功，访问地址: http://localhost:{}", port);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建百度代理配置失败: " + e.getMessage());
            
            log.error("创建百度代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 快速创建测试代理配置
     */
    @PostMapping("/create-test")
    public ResponseEntity<Map<String, Object>> createTestProxy(@RequestParam(defaultValue = "8081") Integer port) {
        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = new WebProxyConfig();
            config.setProxyName("本地测试服务器");
            config.setProxyPort(port);
            config.setTargetHost("localhost");
            config.setTargetPort(9090);
            config.setTargetProtocol("http");
            config.setWatermarkText("测试水印_{IP}_{TIME}");
            config.setEnablePageWatermark(true);
            config.setEnableApiWatermark(true);
            config.setApiPathPatterns("/api/**,/mock/api/**");
            config.setWatermarkOpacity(0.2);
            config.setWatermarkColor("#FF0000");
            config.setWatermarkAngle(-30.0);
            config.setRemark("用于测试代理和水印功能的本地服务器");
            config.setCreateUser("system");

            WebProxyConfig savedConfig = configService.saveConfig(config);
            result.put("success", true);
            result.put("message", "测试代理配置创建成功");
            result.put("data", savedConfig);
            result.put("accessUrl", "http://localhost:" + port + "/mock/page");
            result.put("testUrls", new String[]{
                "http://localhost:" + port + "/mock/page",
                "http://localhost:" + port + "/mock/api/json",
                "http://localhost:" + port + "/mock/api/xml"
            });

            log.info("创建测试代理配置成功，访问地址: http://localhost:{}/mock/page", port);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建测试代理配置失败: " + e.getMessage());

            log.error("创建测试代理配置失败", e);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 修复指定端口的配置，启用页面水印
     */
    @PostMapping("/fix-watermark/{port}")
    public ResponseEntity<Map<String, Object>> fixWatermarkConfig(@PathVariable Integer port) {
        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }

            // 强制启用页面水印和API水印
            config.setEnablePageWatermark(Boolean.TRUE);
            config.setEnableApiWatermark(Boolean.TRUE);

            // 设置水印文本（如果为空）
            if (config.getWatermarkText() == null || config.getWatermarkText().trim().isEmpty()) {
                config.setWatermarkText("API代理_{IP}_{TIME}");
            }

            // 设置API路径模式（如果为空）
            if (config.getApiPathPatterns() == null || config.getApiPathPatterns().trim().isEmpty()) {
                config.setApiPathPatterns("/api/**,/rest/**,/service/**,/auth/**");
            }

            // 设置其他水印参数
            if (config.getWatermarkOpacity() == null) {
                config.setWatermarkOpacity(0.15);
            }
            if (config.getWatermarkColor() == null) {
                config.setWatermarkColor("#FF0000");
            }
            if (config.getWatermarkAngle() == null) {
                config.setWatermarkAngle(-30.0);
            }

            WebProxyConfig savedConfig = configService.saveConfig(config);

            result.put("success", true);
            result.put("message", "配置修复成功，已启用页面水印和API水印");
            result.put("data", savedConfig);

            log.info("修复端口{}的配置成功: 页面水印={}, API水印={}, 水印文本={}",
                    port, savedConfig.getEnablePageWatermark(),
                    savedConfig.getEnableApiWatermark(), savedConfig.getWatermarkText());

        } catch (Exception e) {
            log.error("修复端口{}的配置失败", port, e);
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 测试HTTPS连接
     */
    @PostMapping("/test-https")
    public ResponseEntity<Map<String, Object>> testHttpsConnection(
            @RequestParam String targetUrl,
            @RequestParam(defaultValue = "10000") Integer timeout) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("测试HTTPS连接: {}", targetUrl);

            // 使用RestTemplate测试连接
            org.springframework.web.client.RestTemplate testTemplate = new org.springframework.web.client.RestTemplate();

            // 设置超时
            org.springframework.http.client.HttpComponentsClientHttpRequestFactory factory =
                new org.springframework.http.client.HttpComponentsClientHttpRequestFactory();
            factory.setConnectTimeout(timeout);
            factory.setReadTimeout(timeout);
            testTemplate.setRequestFactory(factory);

            long startTime = System.currentTimeMillis();
            org.springframework.http.ResponseEntity<String> response = testTemplate.getForEntity(targetUrl, String.class);
            long endTime = System.currentTimeMillis();

            result.put("success", true);
            result.put("statusCode", response.getStatusCode().value());
            result.put("statusText", response.getStatusCode().getReasonPhrase());
            result.put("responseTime", endTime - startTime);
            result.put("contentLength", response.getBody() != null ? response.getBody().length() : 0);
            result.put("headers", response.getHeaders().toSingleValueMap());

            log.info("HTTPS连接测试成功: {} - 状态码: {}, 响应时间: {}ms",
                    targetUrl, response.getStatusCode(), endTime - startTime);

        } catch (Exception e) {
            log.error("HTTPS连接测试失败: {} - {}", targetUrl, e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getClass().getSimpleName());
            result.put("message", e.getMessage());

            // 提供针对性的解决建议
            if (e instanceof java.net.ConnectException) {
                result.put("suggestion", "无法连接到目标服务器，请检查URL和网络连接");
            } else if (e instanceof java.net.SocketTimeoutException) {
                result.put("suggestion", "连接超时，请检查网络状况或增加超时时间");
            } else if (e instanceof javax.net.ssl.SSLException) {
                result.put("suggestion", "SSL证书验证失败，请检查HTTPS配置");
            } else {
                result.put("suggestion", "请检查目标URL是否正确且可访问");
            }
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 检查API路径匹配
     */
    @PostMapping("/check-api-path")
    public ResponseEntity<Map<String, Object>> checkApiPath(
            @RequestParam Integer port,
            @RequestParam String path) {

        Map<String, Object> result = new HashMap<>();

        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }

            // 检查API路径模式
            String apiPatterns = config.getApiPathPatterns();
            result.put("apiPathPatterns", apiPatterns);
            result.put("enableApiWatermark", config.getEnableApiWatermark());

            // 检查路径是否匹配
            boolean matches = false;
            if (apiPatterns != null && !apiPatterns.trim().isEmpty()) {
                String[] patterns = apiPatterns.split(",");
                org.springframework.util.AntPathMatcher pathMatcher = new org.springframework.util.AntPathMatcher();

                for (String pattern : patterns) {
                    if (pattern != null && !pattern.trim().isEmpty()) {
                        boolean patternMatches = pathMatcher.match(pattern.trim(), path);
                        result.put("pattern_" + pattern.trim(), patternMatches);
                        if (patternMatches) {
                            matches = true;
                        }
                    }
                }
            }

            result.put("pathMatches", matches);
            result.put("success", true);

            if (!matches) {
                result.put("suggestion", "路径 '" + path + "' 不匹配任何API模式，请检查配置");
            }

        } catch (Exception e) {
            log.error("检查API路径匹配失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
