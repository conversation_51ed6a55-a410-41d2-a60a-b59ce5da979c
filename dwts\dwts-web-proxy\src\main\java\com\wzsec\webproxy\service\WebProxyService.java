package com.wzsec.webproxy.service;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.domain.WebProxyRecord;
import com.wzsec.webproxy.handler.BaiduProxyHandler;
import com.wzsec.webproxy.handler.VueProxyHandler;
import com.wzsec.webproxy.repository.WebProxyRecordRepository;
import com.wzsec.webproxy.watermark.WatermarkProcessor;
import com.wzsec.webproxy.watermark.WatermarkProcessorFactory;
import com.wzsec.webproxy.watermark.impl.HeaderWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.Enumeration;

/**
 * Web代理服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class WebProxyService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WebProxyConfigService configService;

    @Autowired
    private WatermarkProcessorFactory watermarkProcessorFactory;

    @Autowired
    private WebProxyRecordRepository recordRepository;

    @Autowired
    private BaiduProxyHandler baiduProxyHandler;

    @Autowired
    private VueProxyHandler vueProxyHandler;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 处理代理请求
     */
    public ResponseEntity<?> handleProxyRequest(HttpServletRequest request, 
                                              HttpServletResponse response,
                                              byte[] body) throws Exception {
        
        long startTime = System.currentTimeMillis();
        WebProxyRecord record = new WebProxyRecord();
        
        try {
            // 1. 获取代理配置 - 优先通过端口匹配
            WebProxyConfig config = null;

            // 首先尝试通过本地端口获取配置
            int localPort = request.getLocalPort();
            if (localPort != 9090) { // 非主应用端口
                config = configService.getConfigByPort(localPort);
                if (config != null) {
                    log.info("通过端口{}匹配到代理配置: {}", localPort, config.getProxyName());
                }
            }

            // 如果端口匹配失败，使用其他方式
            if (config == null) {
                config = getProxyConfig(request);
            }

            if (config == null || !config.isValid()) {
                log.warn("未找到有效的代理配置，请求路径: {}, Host: {}, LocalPort: {}",
                        request.getRequestURI(), request.getHeader("Host"), localPort);
                return ResponseEntity.notFound().build();
            }

            log.info("使用代理配置: {} ({}:{}) -> {}:{}",
                    config.getProxyName(),
                    localPort,
                    request.getRequestURI(),
                    config.getTargetHost(),
                    config.getTargetPort());
            
            // 2. 初始化访问记录
            initProxyRecord(record, request, config);
            
            // 3. 构建目标URL
            String targetUrl = buildTargetUrl(request, config);
            log.debug("代理目标URL: {}", targetUrl);
            
            // 4. 复制请求头
            HttpHeaders headers = copyRequestHeaders(request, config);
            
            // 5. 创建请求实体
            HttpEntity<byte[]> entity = new HttpEntity<>(body, headers);
            
            // 6. 转发请求到目标服务器
            ResponseEntity<byte[]> targetResponse;
            try {
                log.debug("发送请求到目标服务器: {} {}", request.getMethod(), targetUrl);
                targetResponse = restTemplate.exchange(
                    targetUrl,
                    HttpMethod.valueOf(request.getMethod()),
                    entity,
                    byte[].class
                );
                log.debug("目标服务器响应: {} {}", targetResponse.getStatusCode(),
                         targetResponse.getBody() != null ? targetResponse.getBody().length + " bytes" : "empty");
            } catch (Exception e) {
                log.error("请求目标服务器失败: {} {} - {}", request.getMethod(), targetUrl, e.getMessage(), e);
                throw e;
            }
            
            // 7. 处理响应
            ResponseEntity<?> processedResponse = processResponse(
                targetResponse, request, config, record);
            
            // 8. 更新访问记录
            updateProxyRecord(record, targetResponse, processedResponse, startTime);
            
            return processedResponse;
            
        } catch (Exception e) {
            // 记录错误信息
            String errorMessage = e.getMessage();
            record.setErrorMessage(errorMessage);
            record.setProcessTime(System.currentTimeMillis() - startTime);

            // 针对不同类型的错误提供更详细的日志
            if (config != null) {
                if (e instanceof java.net.ConnectException) {
                    log.error("连接目标服务器失败 - 请检查目标地址和端口: {} -> {}:{}",
                             request.getRequestURI(), config.getTargetHost(), config.getTargetPort(), e);
                } else if (e instanceof java.net.SocketTimeoutException) {
                    log.error("请求目标服务器超时 - 请检查网络连接: {} -> {}:{}",
                             request.getRequestURI(), config.getTargetHost(), config.getTargetPort(), e);
                } else if (e instanceof javax.net.ssl.SSLException) {
                    log.error("SSL连接失败 - 请检查HTTPS配置: {} -> {}://{}:{}",
                             request.getRequestURI(), config.getTargetProtocol(), config.getTargetHost(), config.getTargetPort(), e);
                } else {
                    log.error("代理请求处理失败: {} -> {}://{}:{} - {}",
                             request.getRequestURI(), config.getTargetProtocol(), config.getTargetHost(), config.getTargetPort(), errorMessage, e);
                }
            } else {
                log.error("代理请求处理失败 - 配置为空: {} - {}", request.getRequestURI(), errorMessage, e);
            }
            throw e;
        } finally {
            // 异步保存访问记录
            saveProxyRecordAsync(record);
        }
    }

    /**
     * 获取代理配置
     * 支持多种方式：
     * 1. 通过请求路径前缀 /proxy/{proxyName}
     * 2. 通过Host头中的端口号
     * 3. 通过URL参数指定代理
     * 4. 默认使用百度代理配置
     */
    private WebProxyConfig getProxyConfig(HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        String hostHeader = request.getHeader("Host");

        log.debug("获取代理配置 - 路径: {}, Host: {}", requestPath, hostHeader);

        // 方法1: 通过路径前缀识别代理配置 /proxy/{proxyName}
        if (requestPath.startsWith("/proxy/")) {
            String[] pathParts = requestPath.split("/");
            if (pathParts.length >= 3) {
                String proxyName = pathParts[2];
                WebProxyConfig config = configService.getConfigByName(proxyName);
                if (config != null) {
                    log.info("通过路径前缀找到代理配置: {}", proxyName);
                    return config;
                }
            }
        }

        // 方法2: 通过URL参数指定代理 ?proxy=proxyName
        String proxyParam = request.getParameter("proxy");
        if (proxyParam != null && !proxyParam.trim().isEmpty()) {
            WebProxyConfig config = configService.getConfigByName(proxyParam);
            if (config != null) {
                log.info("通过URL参数找到代理配置: {}", proxyParam);
                return config;
            }
        }

        // 方法3: 通过Host头中的端口号识别
        if (hostHeader != null && hostHeader.contains(":")) {
            try {
                String portStr = hostHeader.split(":")[1];
                Integer port = Integer.parseInt(portStr);
                WebProxyConfig config = configService.getConfigByPort(port);
                if (config != null) {
                    log.info("通过Host端口找到代理配置: {}", port);
                    return config;
                }
            } catch (Exception e) {
                log.debug("解析Host头端口失败: {}", hostHeader);
            }
        }

        // 方法4: 根据请求路径特征智能匹配
        if (requestPath.contains("baidu") || requestPath.contains("百度")) {
            WebProxyConfig config = configService.getConfigByName("百度代理");
            if (config != null) {
                log.info("通过路径特征匹配到百度代理配置");
                return config;
            }
        }

        // 方法5: 默认使用百度代理配置（如果存在）
        WebProxyConfig baiduConfig = configService.getConfigByName("百度代理");
        if (baiduConfig != null) {
            log.info("使用默认百度代理配置");
            return baiduConfig;
        }

        // 方法6: 使用第一个可用配置
        var configs = configService.getAllActiveConfigs();
        if (!configs.isEmpty()) {
            WebProxyConfig config = configs.get(0);
            log.info("使用第一个可用代理配置: {}", config.getProxyName());
            return config;
        }

        log.warn("未找到任何可用的代理配置");
        return null;
    }

    /**
     * 构建目标URL
     */
    private String buildTargetUrl(HttpServletRequest request, WebProxyConfig config) {
        StringBuilder targetUrl = new StringBuilder();

        // 协议和主机
        targetUrl.append(config.getTargetBaseUrl());

        // 处理路径
        String requestPath = request.getRequestURI();

        // 如果请求端口与代理配置端口匹配，说明是直接通过代理端口访问的
        if (request.getLocalPort() == config.getProxyPort()) {
            // 直接代理，保持原路径
            targetUrl.append(requestPath);
        } else {
            // 如果是通过 /proxy/{proxyName} 访问的，需要去掉前缀
            if (requestPath.startsWith("/proxy/")) {
                String[] pathParts = requestPath.split("/", 4); // 分割为最多4部分
                if (pathParts.length >= 4) {
                    // /proxy/{proxyName}/actual/path -> /actual/path
                    requestPath = "/" + pathParts[3];
                } else if (pathParts.length == 3) {
                    // /proxy/{proxyName} -> /
                    requestPath = "/";
                }
            }
            targetUrl.append(requestPath);
        }

        // 查询参数
        if (request.getQueryString() != null) {
            targetUrl.append("?").append(request.getQueryString());
        }

        return targetUrl.toString();
    }

    /**
     * 复制请求头
     */
    private HttpHeaders copyRequestHeaders(HttpServletRequest request, WebProxyConfig config) {
        // 如果是百度请求，使用特殊处理
        if (baiduProxyHandler.isBaiduRequest(config.getTargetHost())) {
            log.info("使用百度特殊请求头处理");
            return baiduProxyHandler.createBaiduHeaders(request);
        }

        HttpHeaders headers = new HttpHeaders();

        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();

            // 跳过一些代理相关的头部
            if (shouldSkipHeader(headerName)) {
                continue;
            }

            Enumeration<String> headerValues = request.getHeaders(headerName);
            while (headerValues.hasMoreElements()) {
                headers.add(headerName, headerValues.nextElement());
            }
        }

        // 添加必要的请求头，避免被目标网站拒绝
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        headers.set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8");
        headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        headers.set("Accept-Encoding", "gzip, deflate, br");
        headers.set("Connection", "keep-alive");
        headers.set("Upgrade-Insecure-Requests", "1");
        headers.set("Sec-Fetch-Dest", "document");
        headers.set("Sec-Fetch-Mode", "navigate");
        headers.set("Sec-Fetch-Site", "none");
        headers.set("Sec-Fetch-User", "?1");
        headers.set("Cache-Control", "max-age=0");

        // 为HTTPS请求设置正确的Host头
        if ("https".equals(config.getTargetProtocol())) {
            String hostValue = config.getTargetHost();
            if (config.getTargetPort() != 443) {
                hostValue += ":" + config.getTargetPort();
            }
            headers.set("Host", hostValue);
        }

        return headers;
    }

    /**
     * 检查是否应该跳过某个请求头
     */
    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") ||
               lowerName.equals("content-length") ||
               lowerName.startsWith("x-forwarded-") ||
               lowerName.equals("connection") ||
               lowerName.equals("upgrade") ||
               lowerName.equals("proxy-connection") ||
               lowerName.equals("transfer-encoding") ||
               lowerName.equals("te");
    }

    /**
     * 处理响应
     */
    private ResponseEntity<?> processResponse(ResponseEntity<byte[]> targetResponse,
                                            HttpServletRequest request,
                                            WebProxyConfig config,
                                            WebProxyRecord record) throws Exception {

        String contentType = getResponseContentType(targetResponse);
        String requestPath = request.getRequestURI();
        byte[] originalContent = targetResponse.getBody();

        log.info("处理响应 - ContentType: {}, Path: {}, EnablePageWatermark: {}, ContentLength: {}",
                contentType, requestPath, config.getEnablePageWatermark(),
                originalContent != null ? originalContent.length : 0);

        // 判断内容类型并选择处理策略
        if (isHtmlContent(contentType)) {
            log.info("处理HTML内容");

            // 检查是否需要Vue特殊处理
            if (vueProxyHandler.needsVueProcessing(config.getTargetHost(), requestPath, contentType)) {
                log.info("使用Vue应用特殊处理");
                return processVueContent(targetResponse, request, config, record);
            } else if (config.getEnablePageWatermark()) {
                log.info("添加页面水印");
                return processHtmlContent(targetResponse, request, config, record);
            } else {
                log.info("直接返回HTML内容");
                return processOtherContent(targetResponse, request, config, record);
            }

        } else if ((vueProxyHandler.isApiRequest(requestPath) || isApiRequest(requestPath, config)) &&
                  (isJsonOrXmlContent(contentType)) &&
                  config.getEnableApiWatermark()) {
            log.info("处理API内容，添加API水印 - 路径: {}", requestPath);
            return processApiContent(targetResponse, request, config, record);

        } else {
            log.info("处理其他内容，可能添加Header水印");
            return processOtherContent(targetResponse, request, config, record);
        }
    }

    /**
     * 处理Vue应用内容
     */
    private ResponseEntity<?> processVueContent(ResponseEntity<byte[]> response,
                                              HttpServletRequest request,
                                              WebProxyConfig config,
                                              WebProxyRecord record) throws Exception {

        String originalContent = safeToString(response.getBody());
        log.info("处理Vue内容，原始长度: {}", originalContent.length());

        // 1. Vue应用链接重写
        String processedContent = vueProxyHandler.processVueContent(
                originalContent, request, config.getTargetHost(), config.getTargetPort());

        // 2. 添加水印（如果启用）
        if (config.getEnablePageWatermark()) {
            WatermarkProcessor processor = watermarkProcessorFactory.getHtmlProcessor();
            if (processor != null) {
                byte[] watermarkedContent = processor.processWatermark(
                        safeToBytes(processedContent),
                        getResponseContentType(response),
                        request,
                        config);
                processedContent = safeToString(watermarkedContent);
                log.info("Vue内容水印处理完成，最终长度: {}", processedContent.length());
            }
        }

        // 3. 更新记录
        record.setRequestType("VUE_PAGE");
        record.setWatermarkAdded(config.getEnablePageWatermark());
        record.setWatermarkType("PAGE");
        record.setResponseSize((long) processedContent.length());
        recordRepository.save(record);

        // 4. 构建响应
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        responseHeaders.set("Content-Type", "text/html; charset=UTF-8");
        responseHeaders.set("Content-Length", String.valueOf(processedContent.length()));

        // 添加水印标识头
        if (config.getEnablePageWatermark()) {
            responseHeaders.set("X-DWTS-Watermark", "Vue-Page");
            responseHeaders.set("X-DWTS-Watermark-Time", String.valueOf(System.currentTimeMillis()));
        }

        return ResponseEntity.status(response.getStatusCode())
                .headers(responseHeaders)
                .body(processedContent);
    }

    /**
     * 处理HTML内容
     */
    private ResponseEntity<?> processHtmlContent(ResponseEntity<byte[]> response,
                                               HttpServletRequest request,
                                               WebProxyConfig config,
                                               WebProxyRecord record) throws Exception {
        
        WatermarkProcessor processor = watermarkProcessorFactory.getHtmlProcessor();
        byte[] watermarkedContent = processor.processWatermark(
            response.getBody(),
            getResponseContentType(response),
            request,
            config
        );
        
        // 更新记录
        record.setRequestType("PAGE");
        record.setWatermarkAdded(true);
        record.setWatermarkType("PAGE");
        record.setResponseSize((long) watermarkedContent.length);
        
        // 更新响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        responseHeaders.setContentLength(watermarkedContent.length);
        
        return new ResponseEntity<>(watermarkedContent, responseHeaders, response.getStatusCode());
    }

    /**
     * 处理API内容
     */
    private ResponseEntity<?> processApiContent(ResponseEntity<byte[]> response,
                                              HttpServletRequest request,
                                              WebProxyConfig config,
                                              WebProxyRecord record) throws Exception {
        
        String contentType = getResponseContentType(response);
        WatermarkProcessor processor = watermarkProcessorFactory.getProcessor(contentType);
        
        byte[] watermarkedContent = processor.processWatermark(
            response.getBody(),
            contentType,
            request,
            config
        );
        
        // 更新记录
        record.setRequestType("API");
        record.setWatermarkAdded(true);
        record.setWatermarkType(processor.getWatermarkType());
        record.setResponseSize((long) watermarkedContent.length);
        
        // 更新响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        responseHeaders.setContentLength(watermarkedContent.length);
        
        return new ResponseEntity<>(watermarkedContent, responseHeaders, response.getStatusCode());
    }

    /**
     * 处理其他内容
     */
    private ResponseEntity<?> processOtherContent(ResponseEntity<byte[]> response,
                                                HttpServletRequest request,
                                                WebProxyConfig config,
                                                WebProxyRecord record) throws Exception {
        
        // 更新记录
        record.setRequestType("RESOURCE");
        record.setWatermarkAdded(false);
        record.setResponseSize(response.getBody() != null ? (long) response.getBody().length : 0L);
        
        // 添加Header水印（如果启用）
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.putAll(response.getHeaders());
        
        if (config.getEnableApiWatermark()) {
            HeaderWatermarkProcessor headerProcessor = watermarkProcessorFactory.getHeaderProcessor();
            String watermarkHeader = headerProcessor.generateWatermarkHeader(request, config);
            
            responseHeaders.add("X-DWTS-Watermark", watermarkHeader);
            responseHeaders.add("X-DWTS-Timestamp", String.valueOf(System.currentTimeMillis()));
            responseHeaders.add("X-DWTS-IP", getClientIpAddress(request));
            
            record.setWatermarkAdded(true);
            record.setWatermarkType("HEADER");
        }
        
        return new ResponseEntity<>(response.getBody(), responseHeaders, response.getStatusCode());
    }

    // 其他辅助方法...
    private String getResponseContentType(ResponseEntity<byte[]> response) {
        MediaType contentType = response.getHeaders().getContentType();
        return contentType != null ? contentType.toString() : null;
    }

    private boolean isHtmlContent(String contentType) {
        return contentType != null && contentType.toLowerCase().contains("text/html");
    }

    private boolean isJsonOrXmlContent(String contentType) {
        if (contentType == null) return false;
        String lowerType = contentType.toLowerCase();
        return lowerType.contains("application/json") || 
               lowerType.contains("application/xml") || 
               lowerType.contains("text/xml");
    }

    private boolean isApiRequest(String requestPath, WebProxyConfig config) {
        if (config == null || config.getApiPathPatterns() == null || config.getApiPathPatterns().trim().isEmpty()) {
            return false;
        }

        String[] patterns = config.getApiPathPatterns().split(",");
        for (String pattern : patterns) {
            if (pattern != null && !pattern.trim().isEmpty() && pathMatcher.match(pattern.trim(), requestPath)) {
                return true;
            }
        }
        return false;
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        String remoteAddr = request.getRemoteAddr();
        if ("127.0.0.1".equals(remoteAddr) || "0:0:0:0:0:0:0:1".equals(remoteAddr)) {
            try {
                remoteAddr = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("获取本机IP失败", e);
            }
        }
        
        return remoteAddr;
    }

    private void initProxyRecord(WebProxyRecord record, HttpServletRequest request, WebProxyConfig config) {
        record.setProxyConfigId(config.getId());
        record.setRequestIp(getClientIpAddress(request));
        record.setRequestPort(request.getRemotePort());
        record.setRequestPath(request.getRequestURI());
        record.setRequestMethod(request.getMethod());
        record.setUserAgent(request.getHeader("User-Agent"));
        record.setReferer(request.getHeader("Referer"));
        
        if (request.getSession(false) != null) {
            record.setSessionId(request.getSession().getId());
        }
    }

    private void updateProxyRecord(WebProxyRecord record, 
                                 ResponseEntity<byte[]> targetResponse,
                                 ResponseEntity<?> processedResponse,
                                 long startTime) {
        record.setResponseStatus(targetResponse.getStatusCodeValue());
        record.setResponseContentType(getResponseContentType(targetResponse));
        record.setProcessTime(System.currentTimeMillis() - startTime);
    }

    private void saveProxyRecordAsync(WebProxyRecord record) {
        try {
            recordRepository.save(record);
        } catch (Exception e) {
            log.error("保存代理访问记录失败", e);
        }
    }

    /**
     * 安全地转换字节数组为字符串
     *
     * @param content 字节数组
     * @return 字符串
     */
    private String safeToString(byte[] content) {
        if (content == null || content.length == 0) {
            return "";
        }
        return new String(content, StandardCharsets.UTF_8);
    }

    /**
     * 安全地转换字符串为字节数组
     *
     * @param content 字符串
     * @return 字节数组
     */
    private byte[] safeToBytes(String content) {
        if (content == null) {
            return new byte[0];
        }
        return content.getBytes(StandardCharsets.UTF_8);
    }
}
