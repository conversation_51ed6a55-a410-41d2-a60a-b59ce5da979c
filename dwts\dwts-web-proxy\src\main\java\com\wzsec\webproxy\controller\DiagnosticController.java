package com.wzsec.webproxy.controller;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.service.WebProxyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 诊断控制器 - 用于排查代理和水印问题
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/diagnostic")
public class DiagnosticController {

    @Autowired
    private WebProxyConfigService configService;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 系统诊断
     */
    @GetMapping("/system")
    public ResponseEntity<Map<String, Object>> systemDiagnostic() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 基本系统信息
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("osName", System.getProperty("os.name"));
            systemInfo.put("osVersion", System.getProperty("os.version"));
            systemInfo.put("availableProcessors", Runtime.getRuntime().availableProcessors());
            systemInfo.put("maxMemory", Runtime.getRuntime().maxMemory() / 1024 / 1024 + " MB");
            systemInfo.put("freeMemory", Runtime.getRuntime().freeMemory() / 1024 / 1024 + " MB");
            
            try {
                systemInfo.put("localHost", InetAddress.getLocalHost().getHostAddress());
            } catch (Exception e) {
                systemInfo.put("localHost", "unknown");
            }
            
            result.put("systemInfo", systemInfo);
            
            // 代理配置信息
            List<WebProxyConfig> configs = configService.getAllActiveConfigs();
            Map<String, Object> proxyInfo = new HashMap<>();
            proxyInfo.put("totalConfigs", configs.size());
            proxyInfo.put("configs", configs);
            result.put("proxyInfo", proxyInfo);
            
            // 配置统计
            result.put("configStats", configService.getConfigStats());
            
            result.put("success", true);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("系统诊断失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 网络连接测试
     */
    @PostMapping("/network-test")
    public ResponseEntity<Map<String, Object>> networkTest(
            @RequestParam String host,
            @RequestParam Integer port,
            @RequestParam(defaultValue = "http") String protocol) {
        
        Map<String, Object> result = new HashMap<>();
        String targetUrl = protocol + "://" + host + ":" + port;
        
        try {
            log.info("测试网络连接: {}", targetUrl);
            
            long startTime = System.currentTimeMillis();
            
            // 测试基本连接
            try {
                InetAddress address = InetAddress.getByName(host);
                result.put("hostResolved", true);
                result.put("hostAddress", address.getHostAddress());
                result.put("hostReachable", address.isReachable(5000));
            } catch (Exception e) {
                result.put("hostResolved", false);
                result.put("hostError", e.getMessage());
            }
            
            // 测试HTTP/HTTPS连接
            try {
                ResponseEntity<String> response = restTemplate.getForEntity(targetUrl, String.class);
                long endTime = System.currentTimeMillis();
                
                result.put("httpSuccess", true);
                result.put("statusCode", response.getStatusCode().value());
                result.put("responseTime", endTime - startTime);
                result.put("contentLength", response.getBody() != null ? response.getBody().length() : 0);
                result.put("headers", response.getHeaders().toSingleValueMap());
                
            } catch (Exception e) {
                result.put("httpSuccess", false);
                result.put("httpError", e.getClass().getSimpleName());
                result.put("httpMessage", e.getMessage());
            }
            
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("网络测试失败: {}", targetUrl, e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 水印功能测试
     */
    @GetMapping("/watermark-test/{port}")
    public ResponseEntity<Map<String, Object>> watermarkTest(@PathVariable Integer port) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            WebProxyConfig config = configService.getConfigByPort(port);
            if (config == null) {
                result.put("success", false);
                result.put("message", "未找到端口 " + port + " 的配置");
                return ResponseEntity.ok(result);
            }
            
            Map<String, Object> watermarkInfo = new HashMap<>();
            watermarkInfo.put("enablePageWatermark", config.getEnablePageWatermark());
            watermarkInfo.put("enableApiWatermark", config.getEnableApiWatermark());
            watermarkInfo.put("watermarkText", config.getWatermarkText());
            watermarkInfo.put("watermarkOpacity", config.getWatermarkOpacity());
            watermarkInfo.put("watermarkColor", config.getWatermarkColor());
            watermarkInfo.put("watermarkAngle", config.getWatermarkAngle());
            watermarkInfo.put("watermarkWidth", config.getWatermarkWidth());
            watermarkInfo.put("watermarkHeight", config.getWatermarkHeight());
            watermarkInfo.put("apiPathPatterns", config.getApiPathPatterns());
            
            result.put("config", watermarkInfo);
            result.put("success", true);
            
            // 提供建议
            if (!config.getEnablePageWatermark() && !config.getEnableApiWatermark()) {
                result.put("suggestion", "页面水印和API水印都未启用，请启用至少一种水印类型");
            } else if (config.getWatermarkText() == null || config.getWatermarkText().trim().isEmpty()) {
                result.put("suggestion", "水印文本为空，请设置水印文本");
            } else {
                result.put("suggestion", "水印配置正常");
            }
            
        } catch (Exception e) {
            log.error("水印测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 端口连通性测试
     */
    @PostMapping("/port-test")
    public ResponseEntity<Map<String, Object>> portTest(
            @RequestParam String host,
            @RequestParam Integer port,
            @RequestParam(defaultValue = "5000") Integer timeout) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试端口连通性: {}:{}", host, port);
            
            long startTime = System.currentTimeMillis();
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress(host, port), timeout);
            long endTime = System.currentTimeMillis();
            socket.close();
            
            result.put("success", true);
            result.put("connected", true);
            result.put("responseTime", endTime - startTime);
            result.put("message", "端口连接成功");
            
        } catch (Exception e) {
            log.error("端口测试失败: {}:{}", host, port, e);
            result.put("success", true);
            result.put("connected", false);
            result.put("error", e.getClass().getSimpleName());
            result.put("message", e.getMessage());
            
            if (e instanceof java.net.ConnectException) {
                result.put("suggestion", "连接被拒绝，请检查目标服务是否启动");
            } else if (e instanceof java.net.SocketTimeoutException) {
                result.put("suggestion", "连接超时，请检查网络连接或防火墙设置");
            } else {
                result.put("suggestion", "请检查主机地址和端口号是否正确");
            }
        }
        
        return ResponseEntity.ok(result);
    }
}
